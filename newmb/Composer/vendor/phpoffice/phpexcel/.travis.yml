language: php

php:
  - 5.4
  - 5.5
  - 5.6
  - 7.0
  - hhvm
 
matrix:
  allow_failures:
    - php: hhvm

before_script:
  ## Packages
  - sudo apt-get -qq update > /dev/null
  ## Composer
  - composer self-update
  - composer install --prefer-source --dev
  - phpenv global "$TRAVIS_PHP_VERSION"

script:
  ## PHP_CodeSniffer
  - ./vendor/bin/phpcs --report-width=200 --report-summary  --report-full Classes/ unitTests/ --standard=PSR2 -n
  ## PHPUnit
  - phpunit -c ./unitTests/

notifications:
  email: false
