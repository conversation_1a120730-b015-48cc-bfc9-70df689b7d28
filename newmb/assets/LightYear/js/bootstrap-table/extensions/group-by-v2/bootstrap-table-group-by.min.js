/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableGroupBy={exports:{}}.exports}})(this,function(){'use strict';(function(a){var b,c,d=function(a){var b=arguments,c=!0,d=1;return a=a.replace(/%s/g,function(){var a=b[d++];return'undefined'==typeof a?(c=!1,''):a}),c?a:''},e=function(a,b){var c={};return a.forEach(function(a){var d=b(a);c[d]=c[d]||[],c[d].push(a)}),c};a.extend(a.fn.bootstrapTable.defaults,{groupBy:!1,groupByField:'',groupByFormatter:void 0});var f=a.fn.bootstrapTable.Constructor,g=f.prototype.initSort,h=f.prototype.initBody,i=f.prototype.updateSelected;f.prototype.initSort=function(){g.apply(this,Array.prototype.slice.apply(arguments));var d=this;if(c=[],this.options.groupBy&&''!==this.options.groupByField){this.options.sortName!=this.options.groupByField&&this.data.sort(function(c,a){return c[d.options.groupByField].localeCompare(a[d.options.groupByField])});var d=this,b=e(d.data,function(a){return[a[d.options.groupByField]]}),f=0;a.each(b,function(a,b){c.push({id:f,name:a,data:b}),b.forEach(function(a){a._data||(a._data={}),a._data['parent-index']=f}),f++})}},f.prototype.initBody=function(){if(b=!0,h.apply(this,Array.prototype.slice.apply(arguments)),this.options.groupBy&&''!==this.options.groupByField){var e=this,f=!1,g=0;this.columns.forEach(function(a){a.checkbox?f=!0:a.visible&&(g+=1)}),this.options.detailView&&!this.options.cardView&&(g+=1),c.forEach(function(b){var c=[];c.push(d('<tr class="info groupBy expanded" data-group-index="%s">',b.id)),e.options.detailView&&!e.options.cardView&&c.push('<td class="detail"></td>'),f&&c.push('<td class="bs-checkbox">','<input name="btSelectGroup" type="checkbox" />','</td>');var h=b.name;'function'==typeof e.options.groupByFormatter&&(h=e.options.groupByFormatter(b.name,b.id,b.data)),c.push('<td',d(' colspan="%s"',g),'>',h,'</td>'),c.push('</tr>'),e.$body.find('tr[data-parent-index='+b.id+']:first').before(a(c.join('')))}),this.$selectGroup=[],this.$body.find('[name="btSelectGroup"]').each(function(){var b=a(this);e.$selectGroup.push({group:b,item:e.$selectItem.filter(function(){return a(this).closest('tr').data('parent-index')===b.closest('tr').data('group-index')})})}),this.$container.off('click','.groupBy').on('click','.groupBy',function(){a(this).toggleClass('expanded'),e.$body.find('tr[data-parent-index='+a(this).closest('tr').data('group-index')+']').toggleClass('hidden')}),this.$container.off('click','[name="btSelectGroup"]').on('click','[name="btSelectGroup"]',function(b){b.stopImmediatePropagation();var c=a(this),d=c.prop('checked');e[d?'checkGroup':'uncheckGroup'](a(this).closest('tr').data('group-index'))})}b=!1,this.updateSelected()},f.prototype.updateSelected=function(){b||(i.apply(this,Array.prototype.slice.apply(arguments)),this.options.groupBy&&''!==this.options.groupByField&&this.$selectGroup.forEach(function(a){var b=a.item.filter(':enabled').length===a.item.filter(':enabled').filter(':checked').length;a.group.prop('checked',b)}))},f.prototype.getGroupSelections=function(b){var c=this;return a.grep(this.data,function(a){return a[c.header.stateField]&&a._data['parent-index']===b})},f.prototype.checkGroup=function(a){this.checkGroup_(a,!0)},f.prototype.uncheckGroup=function(a){this.checkGroup_(a,!1)},f.prototype.checkGroup_=function(b,c){var d;c||(d=this.getGroupSelections(b)),this.$selectItem.filter(function filter(){return a(this).closest('tr').data('parent-index')===b}).prop('checked',c),this.updateRows(),this.updateSelected(),c&&(d=this.getGroupSelections(b)),this.trigger(c?'check-all':'uncheck-all',d)}})(jQuery)});