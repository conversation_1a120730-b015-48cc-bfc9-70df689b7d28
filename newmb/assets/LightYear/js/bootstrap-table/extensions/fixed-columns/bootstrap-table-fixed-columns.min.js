/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableFixedColumns={exports:{}}.exports}})(this,function(){'use strict';function a(a,b){if(!(a instanceof b))throw new TypeError('Cannot call a class as a function')}function b(a,b){if(!a)throw new ReferenceError('this hasn\'t been initialised - super() hasn\'t been called');return b&&('object'==typeof b||'function'==typeof b)?b:a}function c(a,b){if('function'!=typeof b&&null!==b)throw new TypeError('Super expression must either be null or a function, not '+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}var d=function(){function a(a,b){for(var c,d=0;d<b.length;d++)c=b[d],c.enumerable=c.enumerable||!1,c.configurable=!0,'value'in c&&(c.writable=!0),Object.defineProperty(a,c.key,c)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=function a(b,c,d){null===b&&(b=Function.prototype);var e=Object.getOwnPropertyDescriptor(b,c);if(e===void 0){var f=Object.getPrototypeOf(b);return null===f?void 0:a(f,c,d)}if('value'in e)return e.value;var g=e.get;return void 0===g?void 0:g.call(d)};(function(f){f.extend(f.fn.bootstrapTable.defaults,{fixedColumns:!1,fixedNumber:1}),f.BootstrapTable=function(g){function h(){return a(this,h),b(this,(h.__proto__||Object.getPrototypeOf(h)).apply(this,arguments))}return c(h,g),d(h,[{key:'fitHeader',value:function(){for(var a,b=arguments.length,c=Array(b),d=0;d<b;d++)c[d]=arguments[d];if(((a=e(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),'fitHeader',this)).call.apply(a,[this].concat(c)),!!this.options.fixedColumns)&&!this.$el.is(':hidden')){this.$container.find('.fixed-table-header-columns').remove(),this.$fixedHeader=f('<div class="fixed-table-header-columns"></div>'),this.$fixedHeader.append(this.$tableHeader.find('>table').clone(!0)),this.$tableHeader.after(this.$fixedHeader);var g=this.getFixedColumnsWidth();this.$fixedHeader.css({top:0,width:g,height:this.$tableHeader.outerHeight(!0)}),this.initFixedColumnsBody(),this.$fixedBody.css({top:this.$tableHeader.outerHeight(!0),width:g,height:this.$tableBody.outerHeight(!0)-1}),this.initFixedColumnsEvents()}}},{key:'initBody',value:function(){for(var a,b=arguments.length,c=Array(b),d=0;d<b;d++)c[d]=arguments[d];(a=e(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),'initBody',this)).call.apply(a,[this].concat(c)),!this.options.fixedColumns||this.options.showHeader&&this.options.height||(this.initFixedColumnsBody(),this.$fixedBody.css({top:0,width:this.getFixedColumnsWidth(),height:this.$tableHeader.outerHeight(!0)+this.$tableBody.outerHeight(!0)}),this.initFixedColumnsEvents())}},{key:'initFixedColumnsBody',value:function(){this.$container.find('.fixed-table-body-columns').remove(),this.$fixedBody=f('<div class="fixed-table-body-columns"></div>'),this.$fixedBody.append(this.$tableBody.find('>table').clone(!0)),this.$tableBody.after(this.$fixedBody)}},{key:'getFixedColumnsWidth',value:function(){for(var a=this.getVisibleFields(),b=0,c=0;c<this.options.fixedNumber;c++)b+=this.$header.find('th[data-field="'+a[c]+'"]').outerWidth(!0);return b+1}},{key:'initFixedColumnsEvents',value:function(){var a=this;this.$tableBody.off('scroll.fixed-columns').on('scroll.fixed-columns',function(b){a.$fixedBody.find('table').css('top',-f(b.currentTarget).scrollTop())}),this.$body.find('> tr[data-index]').off('hover').hover(function(b){var c=f(b.currentTarget).data('index');a.$fixedBody.find('tr[data-index="'+c+'"]').css('background-color',f(b.currentTarget).css('background-color'))},function(b){var c=f(b.currentTarget).data('index'),d=a.$fixedBody.find('tr[data-index="'+c+'"]');d.attr('style',d.attr('style').replace(/background-color:.*;/,''))}),this.$fixedBody.find('tr[data-index]').off('hover').hover(function(b){var c=f(b.currentTarget).data('index');a.$body.find('tr[data-index="'+c+'"]').css('background-color',f(b.currentTarget).css('background-color'))},function(b){var c=f(b.currentTarget).data('index'),d=a.$body.find('> tr[data-index="'+c+'"]');d.attr('style',d.attr('style').replace(/background-color:.*;/,''))})}}]),h}(f.BootstrapTable)})(jQuery)});