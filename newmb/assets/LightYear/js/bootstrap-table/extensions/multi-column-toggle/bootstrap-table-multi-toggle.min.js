/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableMultiToggle={exports:{}}.exports}})(this,function(){'use strict';!function(a){var b=a.fn.bootstrapTable.utils.sprintf,c=function(a){a.initHeader(),a.initSearch(),a.initPagination(),a.initBody()};a.extend(a.fn.bootstrapTable.defaults,{showToggleBtn:!1,multiToggleDefaults:[]}),a.fn.bootstrapTable.methods.push('hideAllColumns','showAllColumns');var d=a.fn.bootstrapTable.Constructor,e=d.prototype.initToolbar;d.prototype.initToolbar=function(){e.apply(this,Array.prototype.slice.apply(arguments));var a=this,b=this.$toolbar.find('>.btn-group');if('string'==typeof this.options.multiToggleDefaults&&(this.options.multiToggleDefaults=JSON.parse(this.options.multiToggleDefaults)),this.options.showToggleBtn&&this.options.showColumns){b.append('<button class=\'btn btn-default hidden\' id=\'showAllBtn\'><span class=\'glyphicon glyphicon-resize-full icon-zoom-in\'></span></button>'+'<button class=\'btn btn-default\' id=\'hideAllBtn\'><span class=\'glyphicon glyphicon-resize-small icon-zoom-out\'></span></button>'),b.find('#showAllBtn').click(function(){a.showAllColumns(),b.find('#hideAllBtn').toggleClass('hidden'),b.find('#showAllBtn').toggleClass('hidden')}),b.find('#hideAllBtn').click(function(){a.hideAllColumns(),b.find('#hideAllBtn').toggleClass('hidden'),b.find('#showAllBtn').toggleClass('hidden')})}},d.prototype.hideAllColumns=function(){var d=this,e=d.options.multiToggleDefaults;a.each(this.columns,function(a,c){if(-1==e.indexOf(c.field)&&c.switchable){c.visible=!1;var f=d.$toolbar.find('.keep-open input').prop('disabled',!1);f.filter(b('[value="%s"]',a)).prop('checked',!1)}}),c(d)},d.prototype.showAllColumns=function(){var d=this;a.each(this.columns,function(a,c){c.switchable&&(c.visible=!0);var e=d.$toolbar.find('.keep-open input').prop('disabled',!1);e.filter(b('[value="%s"]',a)).prop('checked',!0)}),c(d),d.toggleColumn(0,d.columns[0].visible,!1)}}(jQuery)});