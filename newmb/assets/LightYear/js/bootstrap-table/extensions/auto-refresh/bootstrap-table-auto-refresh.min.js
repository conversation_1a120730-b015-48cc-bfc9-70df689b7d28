/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableAutoRefresh={exports:{}}.exports}})(this,function(){'use strict';function a(a,b){if(!(a instanceof b))throw new TypeError('Cannot call a class as a function')}function b(a,b){if(!a)throw new ReferenceError('this hasn\'t been initialised - super() hasn\'t been called');return b&&('object'==typeof b||'function'==typeof b)?b:a}function c(a,b){if('function'!=typeof b&&null!==b)throw new TypeError('Super expression must either be null or a function, not '+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}var d=function(){function a(a,b){for(var c,d=0;d<b.length;d++)c=b[d],c.enumerable=c.enumerable||!1,c.configurable=!0,'value'in c&&(c.writable=!0),Object.defineProperty(a,c.key,c)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=function a(b,c,d){null===b&&(b=Function.prototype);var e=Object.getOwnPropertyDescriptor(b,c);if(e===void 0){var f=Object.getPrototypeOf(b);return null===f?void 0:a(f,c,d)}if('value'in e)return e.value;var g=e.get;return void 0===g?void 0:g.call(d)};(function(f){var g=f.fn.bootstrapTable.utils;f.extend(f.fn.bootstrapTable.defaults,{autoRefresh:!1,autoRefreshInterval:60,autoRefreshSilent:!0,autoRefreshStatus:!0,autoRefreshFunction:null}),f.extend(f.fn.bootstrapTable.defaults.icons,{autoRefresh:4===g.bootstrapVersion?'fa-clock':'glyphicon-time icon-time'}),f.extend(f.fn.bootstrapTable.locales,{formatAutoRefresh:function(){return'Auto Refresh'}}),f.extend(f.fn.bootstrapTable.defaults,f.fn.bootstrapTable.locales),f.BootstrapTable=function(h){function i(){return a(this,i),b(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}return c(i,h),d(i,[{key:'init',value:function(){for(var a,b=this,c=arguments.length,d=Array(c),f=0;f<c;f++)d[f]=arguments[f];(a=e(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),'init',this)).call.apply(a,[this].concat(d)),this.options.autoRefresh&&this.options.autoRefreshStatus&&(this.options.autoRefreshFunction=setInterval(function(){b.refresh({silent:b.options.autoRefreshSilent})},1e3*this.options.autoRefreshInterval))}},{key:'initToolbar',value:function(){for(var a,b=arguments.length,c=Array(b),d=0;d<b;d++)c[d]=arguments[d];if((a=e(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),'initToolbar',this)).call.apply(a,[this].concat(c)),this.options.autoRefresh){var h=this.$toolbar.find('>.btn-group'),j=h.find('.auto-refresh');j.length||(j=f('\n            <button class="auto-refresh btn'+g.sprintf(' btn-%s',this.options.buttonsClass)+'\n            '+g.sprintf(' btn-%s',this.options.iconSize)+'\n            '+(this.options.autoRefreshStatus?'active':'')+'"\n            type="button" title="'+this.options.formatAutoRefresh()+'">\n            <i class="'+this.options.iconsPrefix+' '+this.options.icons.autoRefresh+'"></i>\n            </button>\n          ').appendTo(h),j.on('click',f.proxy(this.toggleAutoRefresh,this)))}}},{key:'toggleAutoRefresh',value:function(){var a=this;this.options.autoRefresh&&(this.options.autoRefreshStatus?(clearInterval(this.options.autoRefreshFunction),this.$toolbar.find('>.btn-group').find('.auto-refresh').removeClass('active')):(this.options.autoRefreshFunction=setInterval(function(){a.refresh({silent:a.options.autoRefreshSilent})},1e3*this.options.autoRefreshInterval),this.$toolbar.find('>.btn-group').find('.auto-refresh').addClass('active')),this.options.autoRefreshStatus=!this.options.autoRefreshStatus)}}]),i}(f.BootstrapTable)})(jQuery)});