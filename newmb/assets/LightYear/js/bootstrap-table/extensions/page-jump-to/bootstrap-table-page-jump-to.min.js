/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTablePageJumpTo={exports:{}}.exports}})(this,function(){'use strict';(function(a){var b=a.fn.bootstrapTable.utils.sprintf;a.extend(a.fn.bootstrapTable.defaults,{showJumpto:!1,exportOptions:{}}),a.extend(a.fn.bootstrapTable.locales,{formatJumpto:function(){return'GO'}}),a.extend(a.fn.bootstrapTable.defaults,a.fn.bootstrapTable.locales);var c=a.fn.bootstrapTable.Constructor,d=c.prototype.initPagination;c.prototype.initPagination=function(){if(d.apply(this,Array.prototype.slice.apply(arguments)),this.options.showJumpto){var c=this,e=this.$pagination.find('ul.pagination'),f=e.find('li.jumpto');f.length||(f=a(['<li class="jumpto">','<input type="text" class="form-control">','<button class="btn'+b(' btn-%s',this.options.buttonsClass)+b(' btn-%s',this.options.iconSize)+'" title="'+this.options.formatJumpto()+'"  type="button">'+this.options.formatJumpto(),'</button>','</li>'].join('')).appendTo(e),f.find('button').click(function(){c.selectPage(parseInt(f.find('input').val()))}))}}})(jQuery)});