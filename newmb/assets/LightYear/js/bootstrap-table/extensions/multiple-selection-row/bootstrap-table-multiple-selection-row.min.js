/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if("function"==typeof define&&define.amd)define([],b);else if("undefined"!=typeof exports)b();else{b(),a.bootstrapTableMultipleSelectionRow={exports:{}}.exports}})(this,function(){"use strict";!function(a){document.onselectstart=function(){return!1};var b=function(b){return b=a(b),b.is("table")?b:b.parents().find(".table")},c=function(b){return b=a(b),b.parent().parent()},d=function(a){var c=b(a.currentTarget);window.event.ctrlKey&&f(a.currentTarget,c,!1,!1),0===window.event.button&&(!window.event.ctrlKey&&!window.event.shiftKey&&(h(c),f(a.currentTarget,c,!1,!1)),window.event.shiftKey&&g([c.bootstrapTable("getOptions").multipleSelectRowLastSelectedRow.rowIndex,a.currentTarget.rowIndex],c))},e=function(a){var d=b(a.currentTarget);h(d),f(c(a.currentTarget),d,!1,!1)},f=function(b,c,d,e){d?(b=a(b),c.bootstrapTable("getOptions").multipleSelectRowLastSelectedRow=void 0,b.removeClass(c.bootstrapTable("getOptions").multipleSelectRowCssClass),c.bootstrapTable("uncheck",b.data("index"))):(c.bootstrapTable("getOptions").multipleSelectRowLastSelectedRow=b,b=a(b),e?(b.addClass(c.bootstrapTable("getOptions").multipleSelectRowCssClass),c.bootstrapTable("check",b.data("index"))):b.hasClass(c.bootstrapTable("getOptions").multipleSelectRowCssClass)?(b.removeClass(c.bootstrapTable("getOptions").multipleSelectRowCssClass),c.bootstrapTable("uncheck",b.data("index"))):(b.addClass(c.bootstrapTable("getOptions").multipleSelectRowCssClass),c.bootstrapTable("check",b.data("index"))))},g=function(a,b){a.sort(function(c,a){return c-a});for(var c=a[0];c<=a[1];c++)f(b.bootstrapTable("getOptions").multipleSelectRowRows[c-1],b,!1,!0)},h=function(a){for(var b=0;b<a.bootstrapTable("getOptions").multipleSelectRowRows.length;b++)f(a.bootstrapTable("getOptions").multipleSelectRowRows[b],a,!0,!1)};a.extend(a.fn.bootstrapTable.defaults,{multipleSelectRow:!1,multipleSelectRowCssClass:"multiple-select-row-selected",multipleSelectRowLastSelectedRow:void 0,multipleSelectRowRows:[]});var i=a.fn.bootstrapTable.Constructor,j=i.prototype.init,k=i.prototype.initBody;i.prototype.init=function(){if(this.options.multipleSelectRow){var a=this;this.options.multipleSelectRowLastSelectedRow=void 0,this.options.multipleSelectRowRows=[],this.$el.on("post-body.bs.table",function(){setTimeout(function(){a.options.multipleSelectRowRows=a.$body.children(),a.options.multipleSelectRowRows.click(d),a.options.multipleSelectRowRows.find("input[type=checkbox]").change(e)},1)})}j.apply(this,Array.prototype.slice.apply(arguments))},i.prototype.clearAllMultipleSelectionRow=function(){h(this)},a.fn.bootstrapTable.methods.push("clearAllMultipleSelectionRow")}(jQuery)});