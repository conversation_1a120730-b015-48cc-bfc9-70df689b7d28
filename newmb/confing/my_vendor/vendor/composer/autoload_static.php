<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit981cd5fa2b66990a4ec622938110fd6d
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            '<PERSON><PERSON><PERSON>ailer\\PHPMailer\\' => 20,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'PHPMailer\\PHPMailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit981cd5fa2b66990a4ec622938110fd6d::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit981cd5fa2b66990a4ec622938110fd6d::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit981cd5fa2b66990a4ec622938110fd6d::$classMap;

        }, null, ClassLoader::class);
    }
}
