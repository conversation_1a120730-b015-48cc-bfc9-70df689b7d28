/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableGroupBy={exports:{}}.exports}})(this,function(){'use strict';!function(a){var b,c='data-tt-parent-id',d={},e=void 0,f=function(b,c){for(var d=b.$body.find('tr').not('[data-tt-parent-id]'),e=0;e<d.length;e++)if(e===c)return a(d[e]).attr('data-tt-id')},g=function(b,c){var d={};return a.each(c,function(a,c){if(!c.IsParent)for(var e in c)isNaN(parseFloat(c[e]))||b.columns[b.fieldsColumnsIndex[e]].groupBySumGroup&&(void 0===d[e]&&(d[e]=0),d[e]+=+c[e])}),d},h=function(a,f){return b.apply([a,f]),d['data-tt-id'.toString()]=f,a.IsParent?(e=f,delete d[c.toString()]):d[c.toString()]=void 0==e?f:e,d},i=function(a,b){for(var c=[],d=0;d<a.options.groupByField.length;d++)c.push(b[a.options.groupByField[d]]);return c},j=function(a,b,c){for(var d={},e=0;e<a.options.groupByField.length;e++)d[a.options.groupByField[e].toString()]=b[c][0][a.options.groupByField[e]];return d.IsParent=!0,d},k=function(b,c){var d={};return a.each(b,function(a,b){var e=JSON.stringify(c(b));d[e]=d[e]||[],d[e].push(b)}),Object.keys(d).map(function(a){return d[a]})},l=function(b,c){for(var d=[],e={},f=k(c,function(a){return i(b,a)}),h=0;h<f.length;h++)f[h].unshift(j(b,f,h)),b.options.groupBySumGroup&&(e=g(b,f[h]),!a.isEmptyObject(e)&&f[h].push(e));return d=d.concat.apply(d,f),!b.options.loaded&&0<d.length&&(b.options.loaded=!0,b.options.originalData=b.options.data,b.options.data=d),d};a.extend(a.fn.bootstrapTable.defaults,{groupBy:!1,groupByField:[],groupBySumGroup:!1,groupByInitExpanded:void 0,loaded:!1,originalData:void 0}),a.fn.bootstrapTable.methods.push('collapseAll','expandAll','refreshGroupByField'),a.extend(a.fn.bootstrapTable.COLUMN_DEFAULTS,{groupBySumGroup:!1});var m=a.fn.bootstrapTable.Constructor,n=m.prototype.init,o=m.prototype.initData;m.prototype.init=function(){if(!this.options.sortName&&this.options.groupBy&&0<this.options.groupByField.length){var c=this;Object.keys||a.fn.bootstrapTable.utils.objectKeys(),this.options.loaded=!1,this.options.originalData=void 0,b=this.options.rowAttributes,this.options.rowAttributes=h,this.$el.off('post-body.bs.table').on('post-body.bs.table',function(){c.$el.treetable({expandable:!0,onNodeExpand:function(){c.options.height&&c.resetHeader()},onNodeCollapse:function(){c.options.height&&c.resetHeader()}},!0),void 0!==c.options.groupByInitExpanded&&('number'==typeof c.options.groupByInitExpanded?c.expandNode(c.options.groupByInitExpanded):'all'===c.options.groupByInitExpanded.toLowerCase()&&c.expandAll())})}n.apply(this,Array.prototype.slice.apply(arguments))},m.prototype.initData=function(a,b){!this.options.sortName&&this.options.groupBy&&0<this.options.groupByField.length&&(this.options.groupByField='string'==typeof this.options.groupByField?this.options.groupByField.replace('[','').replace(']','').replace(/ /g,'').toLowerCase().split(','):this.options.groupByField,a=l(this,a?a:this.options.data)),o.apply(this,[a,b])},m.prototype.expandAll=function(){this.$el.treetable('expandAll')},m.prototype.collapseAll=function(){this.$el.treetable('collapseAll')},m.prototype.expandNode=function(a){a=f(this,a),a!==void 0&&this.$el.treetable('expandNode',a)},m.prototype.refreshGroupByField=function(b){a.fn.bootstrapTable.utils.compareObjects(this.options.groupByField,b)||(this.options.groupByField=b,this.load(this.options.originalData))}}(jQuery)});