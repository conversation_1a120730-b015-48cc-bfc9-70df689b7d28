// lazyload config

var jp_config = {
  easyPieChart:   [   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/jquery.easy-pie-chart/dist/jquery.easypiechart.fill.js'],
  sparkline:      [   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/jquery.sparkline/dist/jquery.sparkline.retina.js'],
  plot:           [   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/flot/jquery.flot.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/flot/jquery.flot.pie.js', 
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/flot/jquery.flot.resize.js',
					  'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/flot/jquery.flot.time.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/flot.tooltip/js/jquery.flot.tooltip.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/flot.orderbars/js/jquery.flot.orderBars.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/flot-spline/js/jquery.flot.spline.js'],
  moment:         [   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/moment/moment.js'],
  screenfull:     [   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/screenfull/dist/screenfull.min.js'],
  slimScroll:     [   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/slimscroll/jquery.slimscroll.min.js'],
  sortable:       [   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/html5sortable/jquery.sortable.js'],
  nestable:       [   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/nestable/jquery.nestable.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/nestable/jquery.nestable.css'],
  filestyle:      [   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/bootstrap-filestyle/src/bootstrap-filestyle.js'],
  slider:         [   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/bootstrap-slider/bootstrap-slider.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/bootstrap-slider/bootstrap-slider.css'],
  chosen:         [   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/chosen/chosen.jquery.min.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/bootstrap-chosen/bootstrap-chosen.css'],
  TouchSpin:      [   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/bootstrap-touchspin/dist/jquery.bootstrap-touchspin.min.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/bootstrap-touchspin/dist/jquery.bootstrap-touchspin.min.css'],
  wysiwyg:        [   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/bootstrap-wysiwyg/bootstrap-wysiwyg.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/bootstrap-wysiwyg/external/jquery.hotkeys.js'],
  dataTable:      [   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/datatables/media/js/jquery.dataTables.min.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/plugins/integration/bootstrap/3/dataTables.bootstrap.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/plugins/integration/bootstrap/3/dataTables.bootstrap.css'],
  vectorMap:      [   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/bower-jvectormap/jquery-jvectormap-1.2.2.min.js', 
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/bower-jvectormap/jquery-jvectormap-world-mill-en.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/bower-jvectormap/jquery-jvectormap-us-aea-en.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/bower-jvectormap/jquery-jvectormap-1.2.2.css'],
  footable:       [   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/footable/dist/footable.all.min.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/footable/css/footable.core.css'],
  fullcalendar:   [   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/moment/moment.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/fullcalendar/dist/fullcalendar.min.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/fullcalendar/dist/fullcalendar.css',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/fullcalendar/dist/fullcalendar.theme.css'],
  daterangepicker:[   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/moment/moment.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/bootstrap-daterangepicker/daterangepicker.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/bootstrap-daterangepicker/daterangepicker-bs3.css'],
  tagsinput:      [   'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/bootstrap-tagsinput/dist/bootstrap-tagsinput.js',
                      'https://template.down.swap.wang/ui/angulr_2.0.1/bower_components/bootstrap-tagsinput/dist/bootstrap-tagsinput.css']
                      
};
