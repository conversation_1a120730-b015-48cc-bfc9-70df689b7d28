/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if("function"==typeof define&&define.amd)define([],b);else if("undefined"!=typeof exports)b();else{b(),a.bootstrapTableResizable={exports:{}}.exports}})(this,function(){"use strict";(function(a){var b=function(a){!a.options.resizable||a.options.cardView||e(a)||a.$el.resizableColumns()},c=function(a){d(a),b(a)},d=function(a){e(a)&&a.$el.data("resizableColumns").destroy()},e=function(a){return a.$el.data("resizableColumns")!==void 0};a.extend(a.fn.bootstrapTable.defaults,{resizable:!1});var f=a.fn.bootstrapTable.Constructor,g=f.prototype.initBody,h=f.prototype.toggleView,i=f.prototype.resetView;f.prototype.initBody=function(){var a=this;g.apply(this,Array.prototype.slice.apply(arguments)),a.$el.off("column-switch.bs.table, page-change.bs.table").on("column-switch.bs.table, page-change.bs.table",function(){c(a)})},f.prototype.toggleView=function(){h.apply(this,Array.prototype.slice.apply(arguments)),this.options.resizable&&this.options.cardView&&d(this)},f.prototype.resetView=function(){var a=this;i.apply(this,Array.prototype.slice.apply(arguments)),this.options.resizable&&setTimeout(function(){b(a)},100)}})(jQuery)});